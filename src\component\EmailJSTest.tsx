import { useState } from 'react';
import { EmailService } from '../util/emailService';
import { emailConfig } from '../util/emailConfig';

/**
 * Composant de test pour vérifier la configuration EmailJS
 * À utiliser uniquement en développement
 */
const EmailJSTest = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const testConfiguration = async () => {
    setIsLoading(true);
    setTestResult('Test en cours...');

    try {
      // Vérifier que les variables d'environnement sont définies
      if (
        emailConfig.publicKey === 'VOTRE_PUBLIC_KEY' ||
        emailConfig.serviceId === 'VOTRE_SERVICE_ID' ||
        emailConfig.templateId === 'VOTRE_TEMPLATE_ID'
      ) {
        setTestResult('❌ Configuration manquante: Veuillez configurer vos variables d\'environnement EmailJS dans le fichier .env');
        setIsLoading(false);
        return;
      }

      // Test d'envoi d'email
      const testData = {
        name: 'Test Portfolio',
        email: '<EMAIL>',
        subject: 'Test de configuration EmailJS',
        message: 'Ceci est un message de test pour vérifier que EmailJS fonctionne correctement.'
      };

      const result = await EmailService.sendContactEmail(testData);

      if (result.success) {
        setTestResult('✅ Configuration EmailJS réussie ! Vérifiez votre boîte email.');
      } else {
        setTestResult(`❌ Erreur: ${result.message}`);
      }
    } catch (error) {
      setTestResult(`❌ Erreur de test: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const showConfiguration = () => {
    const config = {
      publicKey: emailConfig.publicKey.substring(0, 10) + '...',
      serviceId: emailConfig.serviceId,
      templateId: emailConfig.templateId
    };
    
    setTestResult(`Configuration actuelle:\n${JSON.stringify(config, null, 2)}`);
  };

  // Ne pas afficher en production
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      background: '#f0f0f0',
      border: '1px solid #ccc',
      borderRadius: '8px',
      padding: '15px',
      maxWidth: '300px',
      fontSize: '12px',
      zIndex: 9999,
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>EmailJS Test</h4>
      
      <div style={{ marginBottom: '10px' }}>
        <button
          onClick={testConfiguration}
          disabled={isLoading}
          style={{
            background: '#007bff',
            color: 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            marginRight: '5px'
          }}
        >
          {isLoading ? 'Test...' : 'Tester'}
        </button>
        
        <button
          onClick={showConfiguration}
          style={{
            background: '#6c757d',
            color: 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Config
        </button>
      </div>

      {testResult && (
        <div style={{
          background: testResult.includes('✅') ? '#d4edda' : '#f8d7da',
          color: testResult.includes('✅') ? '#155724' : '#721c24',
          padding: '8px',
          borderRadius: '4px',
          whiteSpace: 'pre-wrap',
          fontSize: '11px'
        }}>
          {testResult}
        </div>
      )}
    </div>
  );
};

export default EmailJSTest;
